import React, { useState, useEffect, useRef } from 'react'

const Toolbar = ({
  fileInputRef,
  onFileUpload,
  csvInputRef,
  onCSVUpload,
  csvFileName,
  roomNames,
  allPdfData,
  currentPdfIndex,
  onSwitchToPdf,
  pdfPages,
  currentPageIndex,
  onSetCurrentPageIndex,
  zoom,
  onSetZoom,
  onAutoFitToScreen,
  onResetCanvasPosition,
  drawingMode,
  onSetDrawingMode,
  currentAnnotation,
  onFinishPolygon,
  rectangleStartPoint,
  onSetRectangleStartPoint,
  onExportAnnotations,
  onExportAnnotatedPDF,
  getCurrentAnnotations,
  selectedAnnotations,
  onCopyAnnotation,
  onDeleteAnnotation,
  showRoomDropdownForAnnotation,
  // Hierarchical filter props
  csvStructure,
  hierarchicalFilter,
  onShowFilterModal,
  // Distance sorting props
  useDistanceSorting,
  onToggleDistanceSorting,
  roomCodeCache,
  // Undo/Redo props
  canUndo,
  canRedo,
  onUndo,
  onRedo
}) => {
  const [showPdfDropdown, setShowPdfDropdown] = useState(false)
  const [showInstructions, setShowInstructions] = useState(false)
  const instructionsRef = useRef(null)
  const pdfDropdownRef = useRef(null)

  // Handle click outside to close instructions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (instructionsRef.current && !instructionsRef.current.contains(event.target)) {
        setShowInstructions(false)
      }
    }

    if (showInstructions) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showInstructions])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (pdfDropdownRef.current && !pdfDropdownRef.current.contains(event.target)) {
        setShowPdfDropdown(false)
      }
    }

    if (showPdfDropdown) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [showPdfDropdown])

  return (
    <div className="toolbar" style={{ color: 'black' }}>
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf"
        multiple
        onChange={onFileUpload}
        style={{ display: 'none' }}
      />

      <input
        ref={csvInputRef}
        type="file"
        accept=".csv"
        onChange={onCSVUpload}
        style={{ display: 'none' }}
      />

      <div className="toolbar-section" >
        <div className="csv-upload-section">
          {!csvFileName && (
            <button
              className="icon-button"
              onClick={() => csvInputRef.current?.click()}
              title="Upload CSV with room names"
              style={{ color: 'black' }}
            >
              <i className="fas fa-table"></i>
            </button>
          )}
          {csvFileName && (
            <span className="csv-status loaded" title={`${roomNames.length} room names loaded from ${csvFileName}`} style={{ color: 'black', backgroundColor: 'white' }}>
              {roomNames.length} rooms
            </span>
          )}
        </div>

        {/* Hierarchical Filter Control */}
        {csvStructure && csvStructure.maxDepth > 1 && (
          <div className="filter-control-section">
            <button
              className={`icon-button ${hierarchicalFilter?.isFilterActive ? 'active' : ''}`}
              onClick={onShowFilterModal}
              title={hierarchicalFilter?.isFilterActive ?
                `Filter active: ${hierarchicalFilter.selectedPath.join(' → ')}` :
                'Set room filter'
              }
              style={{ color: 'black' }}
            >
              <i className="fas fa-filter"></i>
            </button>
            {hierarchicalFilter?.isFilterActive && (
              <div className="filter-status">
                <span className="filter-path" title={hierarchicalFilter.selectedPath.join(' → ')} style={{ color: 'black' }}>
                  {hierarchicalFilter.selectedPath.slice(-2).join(' → ')}
                </span>
                <button
                  className="clear-filter-btn"
                  onClick={() => hierarchicalFilter.clearFilters()}
                  title="Clear filter"
                  style={{ color: 'black' }}
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {allPdfData.current.length > 0 && (
        <>
          {allPdfData.current.length > 1 && (
            <div className="toolbar-section">
              <div className="pdf-dropdown-container" ref={pdfDropdownRef}>
                <button
                  className="icon-button"
                  onClick={() => setShowPdfDropdown(!showPdfDropdown)}
                  title="Select PDF"
                  style={{ color: 'black' }}
                >
                  📋
                </button>
                {showPdfDropdown && (
                  <div className="pdf-dropdown" style={{ color: 'black' }}>
                    {allPdfData.current.map((pdfData, index) => (
                      <div
                        key={index}
                        className={`pdf-dropdown-item ${index === currentPdfIndex ? 'active' : ''}`}
                        onClick={() => {
                          onSwitchToPdf(index)
                          setShowPdfDropdown(false)
                        }}
                        style={{ color: 'black' }}
                      >
                        {pdfData.name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <span style={{ color: 'black' }}>{currentPdfIndex + 1}/{allPdfData.current.length}</span>
            </div>
          )}

          {pdfPages.length > 1 && (
            <div className="toolbar-section">
              <button
                className="icon-button"
                onClick={() => onSetCurrentPageIndex(Math.max(0, currentPageIndex - 1))}
                disabled={currentPageIndex === 0}
                title="Previous Page"
                style={{ color: 'black' }}
              >
                ◀
              </button>
              <span style={{ color: 'black' }}>Page {currentPageIndex + 1}/{pdfPages.length}</span>
              <button
                className="icon-button"
                onClick={() => onSetCurrentPageIndex(Math.min(pdfPages.length - 1, currentPageIndex + 1))}
                disabled={currentPageIndex === pdfPages.length - 1}
                title="Next Page"
                style={{ color: 'black' }}
              >
                ▶
              </button>
            </div>
          )}

          <div className="toolbar-section">
            <button
              className="icon-button"
              onClick={() => onSetZoom(Math.max(0.1, zoom - 0.1))}
              disabled={zoom <= 0.1}
              title="Zoom Out (Mouse wheel)"
              style={{ color: 'black' }}
            >
              <i className="fas fa-search-minus"></i>
            </button>
            <button
              className="icon-button"
              onClick={onAutoFitToScreen}
              title="Fit to Screen"
              style={{ color: 'black' }}
            >
              <i className="fas fa-expand-arrows-alt"></i>
            </button>
            <button
              className="icon-button"
              onClick={() => onSetZoom(1)}
              title="Reset to 100%"
              style={{ color: 'black' }}
            >
              <i className="fas fa-undo"></i>
            </button>
            <button
              className="icon-button"
              onClick={onResetCanvasPosition}
              title="Reset Position"
              style={{ color: 'black' }}
            >
              <i className="fas fa-crosshairs"></i>
            </button>
            <span className="zoom-percentage" style={{ color: 'black' }}>{Math.round(zoom * 100)}%</span>
            <button
              className="icon-button"
              onClick={() => onSetZoom(Math.min(15, zoom + 0.1))}
              disabled={zoom >= 15}
              title="Zoom In (Mouse wheel)"
              style={{ color: 'black' }}
            >
              <i className="fas fa-search-plus"></i>
            </button>
          </div>

          <div className="toolbar-section">
            <button
              className={`icon-button ${drawingMode === 'hand' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('hand')}
              title="Hand Tool (drag to pan)"
              style={{ color: 'black' }}
            >
              <i className="fas fa-hand-paper"></i>
            </button>
            <button
              className={`icon-button ${drawingMode === 'select' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('select')}
              title="Select Tool (click and drag annotations)"
              style={{ color: 'black' }}
            >
              <i className="fas fa-mouse-pointer"></i>
            </button>
            <button
              className={`icon-button ${drawingMode === 'rectangle' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('rectangle')}
              title="Rectangle Tool (click and drag)"
              style={{ color: 'black' }}
            >
              <i className="far fa-square"></i>
            </button>
            <button
              className={`icon-button ${drawingMode === 'polygon' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('polygon')}
              title="Polygon Tool (multi-click)"
              style={{ color: 'black' }}
            >
              <i className="fas fa-draw-polygon"></i>
            </button>
            {currentAnnotation && drawingMode === 'polygon' && (
              <button
                className="icon-button"
                onClick={(event) => {
                  if (roomNames.length > 0 && showRoomDropdownForAnnotation) {
                    // Use room dropdown for polygon
                    onFinishPolygon((annotation) => {
                      showRoomDropdownForAnnotation(annotation, event)
                    })
                  } else {
                    // No CSV loaded, finish normally
                    onFinishPolygon()
                  }
                }}
                title="Finish Polygon (Enter)"
                style={{ color: 'black' }}
              >
                <i className="fas fa-check"></i>
              </button>
            )}
            {rectangleStartPoint && drawingMode === 'rectangle' && (
              <button
                className="icon-button"
                onClick={() => onSetRectangleStartPoint(null)}
                title="Cancel Rectangle (Esc)"
                style={{ color: 'black' }}
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>

          {/* Undo/Redo buttons */}
          <div className="toolbar-section">
            <button
              className="icon-button"
              onClick={onUndo}
              disabled={!canUndo}
              title="Undo (Ctrl+Z)"
              style={{ color: 'black', opacity: canUndo ? 1 : 0.5 }}
            >
              <i className="fas fa-undo"></i>
            </button>
            <button
              className="icon-button"
              onClick={onRedo}
              disabled={!canRedo}
              title="Redo (Ctrl+Y)"
              style={{ color: 'black', opacity: canRedo ? 1 : 0.5 }}
            >
              <i className="fas fa-redo"></i>
            </button>
          </div>

          {/* Distance sorting toggle - only show if room codes are available */}
          {roomCodeCache && roomCodeCache.size > 0 && (
            <div className="toolbar-section">
              <button
                className={`icon-button ${useDistanceSorting ? 'active' : ''}`}
                onClick={onToggleDistanceSorting}
                title={`${useDistanceSorting ? 'Disable' : 'Enable'} distance-based room sorting`}
                style={{ color: 'black' }}
              >
                <i className="fas fa-ruler"></i>
              </button>
            </div>
          )}

          <div className="toolbar-section">

            <div className="info-button-container" ref={instructionsRef}>
              <button
                className={`icon-button info-button ${showInstructions ? 'active' : ''}`}
                onClick={() => setShowInstructions(!showInstructions)}
                title="Instructions and Help"
                style={{ color: 'black' }}
              >
                <i className="fas fa-info-circle"></i>
              </button>
              {showInstructions && (
                <div className="instructions-tooltip" style={{ color: 'black' }}>
                  <div className="instructions-content">
                    <h4>PDF Annotation Tool</h4>
                    <div className="instruction-section">
                      <h5>Tools:</h5>
                      <ul>
                        <li><i className="fas fa-hand-paper"></i> Hand Tool - Drag to pan</li>
                        <li><i className="fas fa-mouse-pointer"></i> Select Tool - Click and drag annotations</li>
                        <li><i className="far fa-square"></i> Rectangle Tool - Click and drag</li>
                        <li><i className="fas fa-draw-polygon"></i> Polygon Tool - Multi-click</li>
                      </ul>
                    </div>
                    <div className="instruction-section">
                      <h5>Shortcuts:</h5>
                      <ul>
                        <li><kbd>H</kbd> - Hand tool</li>
                        <li><kbd>S</kbd> - Select tool</li>
                        <li><kbd>R</kbd> - Rectangle tool</li>
                        <li><kbd>P</kbd> - Polygon tool</li>
                        <li><kbd>Enter</kbd> - Finish polygon</li>
                        <li><kbd>Esc</kbd> - Cancel current action</li>
                        <li><kbd>Ctrl+Z</kbd> - Undo</li>
                        <li><kbd>Ctrl+Y</kbd> - Redo</li>
                        <li><kbd>Ctrl+S</kbd> - Export JSON</li>
                        <li><kbd>Ctrl+C</kbd> - Copy annotation</li>
                        <li><kbd>Del</kbd> - Delete annotation</li>
                      </ul>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          <div className="toolbar-section">
            <button
              className="icon-button"
              onClick={onExportAnnotations}
              disabled={Object.keys(getCurrentAnnotations()).length === 0}
              title="Export JSON (Ctrl+S)"
              style={{ color: 'black' }}
            >
              <i className="fas fa-download"></i>
            </button>
            <button
              className="icon-button"
              onClick={onExportAnnotatedPDF}
              disabled={getCurrentAnnotations().length === 0 || allPdfData.current.length === 0}
              title="Export Annotated PDF"
              style={{ color: 'black' }}
            >
              <i className="fas fa-file-pdf"></i>
            </button>
            {selectedAnnotations && selectedAnnotations.length > 0 && (
              <div className="selected-annotation-info" style={{ color: 'black' }}>
                <span>
                  {selectedAnnotations.length === 1
                    ? selectedAnnotations[0].type
                    : `${selectedAnnotations.length} selected`
                  }
                </span>
                <button
                  className="icon-button"
                  onClick={() => onCopyAnnotation()}
                  title="Copy (Ctrl+C)"
                  style={{ color: 'black' }}
                >
                  <i className="fas fa-copy"></i>
                </button>
                <button
                  className="icon-button"
                  onClick={() => onDeleteAnnotation()}
                  title="Delete (Del)"
                  style={{ color: 'black' }}
                >
                  <i className="fas fa-trash"></i>
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default Toolbar
