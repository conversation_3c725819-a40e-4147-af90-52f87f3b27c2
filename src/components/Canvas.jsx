import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useCanvasDrawing } from './CanvasDrawing'

const Canvas = ({
  pdfPages,
  currentPageIndex,
  annotations,
  selectedAnnotations,
  currentAnnotation,
  polygonPoints,
  rectangleStartPoint,
  drawingMode,
  zoom,
  canvasOffset,
  showZoomIndicator,
  canvasRef, // Accept canvasRef as prop
  onMouseDown,
  onMouseMove,
  onMouseUp,
  onClick,
  onContextMenu,
  onTouchStart,
  onTouchMove,
  onTouchEnd,
  onGestureStart,
  onGestureChange,
  onGestureEnd,
  isDragging,
  isRoomAssignmentActive = false,
  pendingAnnotation = null
}) => {
  const baseImages = useRef({})
  const [currentMousePos, setCurrentMousePos] = useState(null)

  const { drawAnnotationsOnly } = useCanvasDrawing(canvasRef, baseImages, currentPageIndex, pdfPages)

  // Handle mouse move to track position for rectangle preview
  const handleMouseMove = useCallback((event) => {
    if (drawingMode === 'rectangle' && rectangleStartPoint && isDragging) {
      const canvas = canvasRef.current
      if (!canvas) return

      const rect = canvas.getBoundingClientRect()
      const scaleX = canvas.width / rect.width
      const scaleY = canvas.height / rect.height

      const canvasX = (event.clientX - rect.left) * scaleX
      const canvasY = (event.clientY - rect.top) * scaleY

      setCurrentMousePos({ x: canvasX, y: canvasY })
    }

    // Call the original mouse move handler
    onMouseMove(event)
  }, [drawingMode, rectangleStartPoint, isDragging, canvasRef, onMouseMove])

  // Handle mouse leave to stop panning/dragging when mouse exits canvas
  const handleMouseLeave = useCallback((event) => {
    // Call the original mouse up handler to clean up any active operations
    onMouseUp(event)
  }, [onMouseUp])

  // Initialize canvas when PDF pages change
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || pdfPages.length === 0) return

    const currentPage = pdfPages[currentPageIndex]
    if (!currentPage) return

    // Set canvas size immediately
    canvas.width = currentPage.width
    canvas.height = currentPage.height

    // Draw the PDF page immediately
    const ctx = canvas.getContext('2d')
    const img = new Image()
    img.onload = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height)
      ctx.drawImage(img, 0, 0)

      // Store the base image for future use
      baseImages.current[currentPageIndex] = img

      // Redraw annotations if any exist
      if (annotations && annotations.length > 0) {
        drawAnnotationsOnly(
          annotations,
          selectedAnnotations,
          currentAnnotation,
          polygonPoints,
          rectangleStartPoint,
          drawingMode
        )
      }
    }
    img.src = currentPage.imageData
  }, [pdfPages, currentPageIndex])

  // Redraw canvas when annotations change
  useEffect(() => {
    if (annotations && drawAnnotationsOnly && pdfPages.length > 0) {
      // Include pending annotation in the annotations list if it exists
      const allAnnotations = pendingAnnotation
        ? [...annotations, pendingAnnotation]
        : annotations

      drawAnnotationsOnly(
        allAnnotations,
        selectedAnnotations,
        currentAnnotation,
        polygonPoints,
        rectangleStartPoint,
        drawingMode,
        isDragging,
        currentMousePos,
        isRoomAssignmentActive,
        pendingAnnotation
      )
    }
  }, [
    annotations,
    selectedAnnotations,
    currentAnnotation,
    polygonPoints,
    rectangleStartPoint,
    drawingMode,
    isDragging,
    currentMousePos,
    drawAnnotationsOnly,
    isRoomAssignmentActive,
    pendingAnnotation
  ])

  if (pdfPages.length === 0) {
    return (
      <div className="canvas-container">
        <div className="welcome-screen">
          <div className="welcome-content">
            <div className="welcome-icon">📄</div>
            <h1>PDF Annotation Tool</h1>
            <p className="welcome-subtitle">
              Upload a PDF document to start adding annotations
            </p>

            <div className="feature-grid">
              <div className="feature-item">
                <div className="feature-icon">⬜</div>
                <h3>Rectangle Tool</h3>
                <p>Draw rectangular annotations by clicking two points</p>
              </div>
              <div className="feature-item">
                <div className="feature-icon">🔺</div>
                <h3>Polygon Tool</h3>
                <p>Create custom shapes with multiple points</p>
              </div>
              <div className="feature-item">
                <div className="feature-icon">🔲</div>
                <h3>Select Tool</h3>
                <p>Select and move existing annotations around</p>
              </div>
              <div className="feature-item">
                <div className="feature-icon">✋</div>
                <h3>Pan & Zoom</h3>
                <p>Navigate through your document with ease</p>
              </div>
              <div className="feature-item">
                <div className="feature-icon">💾</div>
                <h3>Export Options</h3>
                <p>Save annotations as JSON or annotated PDF</p>
              </div>
            </div>

            <div className="getting-started">
              <h3>Getting Started</h3>
              <ol>
                <li>Click the <strong>📄 Upload PDF</strong> button above</li>
                <li>Select your PDF file from your computer</li>
                <li>Choose an annotation tool (Rectangle, Polygon, or Select)</li>
                <li>Click on the PDF to start annotating or selecting</li>
                <li>Use Select tool to move existing annotations</li>
                <li>Export your work when finished</li>
              </ol>
            </div>

            <div className="keyboard-shortcuts">
              <h3>Keyboard Shortcuts</h3>
              <div className="shortcuts-grid">
                <div><kbd>R</kbd> Rectangle tool</div>
                <div><kbd>P</kbd> Polygon tool</div>
                <div><kbd>S</kbd> Select tool</div>
                <div><kbd>H</kbd> Hand tool</div>
                <div><kbd>Ctrl+Z</kbd> Undo</div>
                <div><kbd>Ctrl+Y</kbd> Redo</div>
                <div><kbd>Ctrl+S</kbd> Export JSON</div>
                <div><kbd>Ctrl+C</kbd> Copy annotation</div>
                <div><kbd>Ctrl+V</kbd> Paste annotation</div>
                <div><kbd>Del</kbd> Delete selected</div>
                <div><kbd>Esc</kbd> Cancel current action</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }



  return (
    <div className="canvas-container">
      <div className={`canvas-wrapper ${isRoomAssignmentActive ? 'room-assignment-active' : ''}`}>
        <canvas
          ref={canvasRef}
          className={`pdf-canvas ${drawingMode === 'hand' ? 'hand-cursor' : ''} ${drawingMode === 'select' ? 'select-cursor' : ''}`}
          style={{
            transform: `translate(${canvasOffset.x}px, ${canvasOffset.y}px) scale(${zoom})`,
            transformOrigin: 'center center',
            touchAction: 'none' // Prevent default touch behaviors
          }}
          onMouseDown={onMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={onMouseUp}
          onMouseLeave={handleMouseLeave}
          onClick={onClick}
          onContextMenu={onContextMenu}
          onTouchStart={onTouchStart}
          onTouchMove={onTouchMove}
          onTouchEnd={onTouchEnd}
          onGestureStart={onGestureStart}
          onGestureChange={onGestureChange}
          onGestureEnd={onGestureEnd}
        />
        {showZoomIndicator && (
          <div className="zoom-indicator">
            {Math.round(zoom * 100)}%
          </div>
        )}
        {annotations && annotations.length > 0 && (
          <div className="annotation-counter">
            {annotations.length} annotation{annotations.length !== 1 ? 's' : ''}
          </div>
        )}
      </div>
    </div>
  )
}

export default Canvas
