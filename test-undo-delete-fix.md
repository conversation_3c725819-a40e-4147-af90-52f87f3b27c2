# Undo/Delete Issue Fix

## Problem
When deleting the last annotation, the second-to-last annotation also gets deleted when using undo.

## Root Cause Analysis
The issue was in the `useUndoRedo` hook implementation. The problem was with stale closures in the `pushToHistory`, `undo`, and `redo` callbacks:

1. **Stale Closure Issue**: The callbacks were capturing stale values of `currentIndex` and `history`
2. **Race Conditions**: Multiple state updates happening simultaneously caused inconsistent state
3. **Dependency Array Issues**: The dependency arrays included state that was being modified inside the callbacks

## Solution
Refactored the `useUndoRedo` hook to use functional state updates:

### Before (Problematic):
```javascript
const pushToHistory = useCallback((state) => {
  setHistory(prev => {
    const newHistory = prev.slice(0, currentIndex + 1) // ❌ Stale currentIndex
    newHistory.push(state)
    setCurrentIndex(newHistory.length - 1) // ❌ Side effect in callback
    return newHistory
  })
}, [currentIndex, maxHistorySize]) // ❌ currentIndex in dependencies
```

### After (Fixed):
```javascript
const pushToHistory = useCallback((state) => {
  setCurrentIndex(prevIndex => {
    setHistory(prevHistory => {
      const newHistory = prevHistory.slice(0, prevIndex + 1) // ✅ Fresh prevIndex
      newHistory.push(state)
      return newHistory
    })
    return prevIndex + 1 // ✅ Return new index
  })
}, [maxHistorySize]) // ✅ No currentIndex dependency
```

## Key Changes

1. **Functional State Updates**: Used `setCurrentIndex(prevIndex => ...)` and `setHistory(prevHistory => ...)`
2. **Removed Stale Dependencies**: Removed `currentIndex` and `history` from dependency arrays
3. **Proper State Coordination**: Ensured state updates happen in the correct order
4. **Eliminated Race Conditions**: Each state update uses the most recent value

## Testing Steps

1. **Load a PDF file**
2. **Create first annotation** - verify undo button appears
3. **Create second annotation** - verify both annotations exist
4. **Delete the second annotation** - verify only first annotation remains
5. **Press undo** - verify second annotation is restored (both should be visible)
6. **Press undo again** - verify first annotation is also restored (both should still be visible)
7. **Press undo again** - verify both annotations are removed (empty state)

## Expected Behavior After Fix

- ✅ Undo button appears after first annotation
- ✅ Deleting last annotation only removes that annotation
- ✅ Undo after deletion properly restores the deleted annotation
- ✅ Multiple undos work correctly in sequence
- ✅ No unexpected annotation disappearances

## Files Modified

- `src/components/AnnotationsHandler.jsx`: Fixed `useUndoRedo` hook implementation
