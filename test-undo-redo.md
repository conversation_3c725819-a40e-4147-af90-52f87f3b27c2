# Undo/Redo Functionality Test

## Test Steps

1. **Load a PDF file**
   - Upload any PDF file to the application

2. **Create some annotations**
   - Create 2-3 rectangle annotations
   - Create 1-2 polygon annotations
   - Delete one annotation
   - Edit an annotation label

3. **Test Undo functionality**
   - Press Ctrl+Z or click the Undo button
   - Verify that the last action is undone
   - Continue pressing Ctrl+Z to undo up to 5 previous actions
   - Verify that the Undo button becomes disabled when no more undos are available

4. **Test Redo functionality**
   - Press Ctrl+Y or click the Redo button
   - Verify that the previously undone action is redone
   - Continue pressing Ctrl+Y to redo actions
   - Verify that the Redo button becomes disabled when no more redos are available

5. **Test History Limit**
   - Create more than 5 annotation changes
   - Verify that only the last 5 changes can be undone

6. **Test UI Elements**
   - Verify that Undo/Redo buttons appear in the toolbar
   - Verify that buttons are properly enabled/disabled based on availability
   - Verify that keyboard shortcuts are listed in the help/instructions

## Expected Behavior

- ✅ Undo/Redo buttons should appear in the toolbar between drawing tools and other sections
- ✅ Buttons should be disabled (grayed out) when no undo/redo is available
- ✅ Ctrl+Z should undo the last annotation change
- ✅ Ctrl+Y should redo the last undone change
- ✅ History should be limited to 5 changes (±5 as requested)
- ✅ Keyboard shortcuts should be documented in help sections
- ✅ Selections should be cleared when undoing/redoing to avoid invalid states

## Implementation Details

- **History Management**: Custom `useUndoRedo` hook with configurable history size (set to 5)
- **State Tracking**: Each annotation change saves the complete annotations state before modification
- **Integration**: Integrated with existing `updateCurrentAnnotations` function
- **UI Integration**: Added buttons to toolbar and updated all help/instruction sections
- **Keyboard Shortcuts**: Added Ctrl+Z (undo) and Ctrl+Y (redo) to existing keyboard handler
