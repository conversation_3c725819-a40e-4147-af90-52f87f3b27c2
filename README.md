# PDF Annotation Web App

A comprehensive web application for annotating PDF documents with rectangles and polygons, built with React and Vite.

## Features

- **Multi-PDF Upload**: Upload multiple PDF files (up to 100) for batch annotation workflow
- **PDF Navigation**: Switch between different PDFs using previous/next buttons or dropdown selector
- **Responsive Design**: Fully adaptive interface that scales perfectly on all screen sizes (mobile to ultra-wide)
- **Floating Toolbar**: Modern centered floating toolbar with intuitive icons
- **Canvas Display**: View PDF pages on an interactive canvas with zoom in/out functionality
- **Navigation Tools**:
  - Hand tool: Drag to pan around the PDF canvas
  - Mouse wheel zoom: Scroll to zoom in/out (no page scrolling)
- **Annotation Tools**:
  - Rectangle annotations: Click and drag to create rectangular annotations
  - Polygon annotations: Click multiple points to create polygon annotations
- **Selection & Movement**: Click to select annotations and drag to move them
- **Copy & Paste**: Copy existing annotations and paste them elsewhere on the canvas
- **Coordinate Preservation**: All annotations maintain accurate coordinates relative to the original PDF dimensions
- **Multi-page Support**: Navigate between PDF pages and annotate each page independently
- **Per-PDF Annotations**: Annotations are stored separately for each PDF and page
- **Export Functionality**:
  - Save annotations as JSON file with coordinates and unique IDs (per PDF)
  - Export annotated PDF with annotation boxes drawn directly on the original PDF

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

## Usage

1. Start the development server:
   ```bash
   npm run dev
   ```

2. Open your browser and navigate to `http://localhost:5173`

3. **Upload PDFs**: Click "Upload PDF" button and select one or multiple PDF files (up to 100)

4. **Navigate Between PDFs**: Use ⏮⏭ buttons or click 📋 dropdown to switch between different PDFs

5. **Navigate Pages**: Use ◀▶ buttons to navigate through multi-page documents

6. **Zoom**: Use 🔍 buttons to adjust the view scale, or 📐 button to auto-fit to screen

7. **Navigate and Annotate**:
   - **Hand Tool (✋)**: Select to drag and pan around the PDF (Press H)
   - **Rectangle**: Select "Rectangle" tool, click two points (top-left and bottom-right corners) (Press R)
   - **Polygon**: Select "Polygon" tool, click multiple points to create vertices, then click "Finish Polygon" or press Enter (Press P)

7. **Select & Move Annotations**:
   - Click on any annotation to select it (highlighted in green)
   - Drag selected annotations to move them around the canvas
   - Selected annotation info appears in the toolbar

8. **Copy & Paste Annotations**:
   - Select an annotation and press Ctrl+C to copy
   - Press Ctrl+V to paste (creates a slightly offset copy)
   - Or use the Copy/Paste buttons in the annotations list

9. **Delete Annotations**:
   - Select an annotation and press Delete/Backspace key
   - Or click "Delete" button next to any annotation in the annotations list

10. **Export Options**:
    - **JSON Export**: Click 💾 button or press Ctrl+S to download JSON with coordinates
    - **Annotated PDF Export**: Click 📄✏️ button to download PDF with annotations drawn on it

## Keyboard Shortcuts

### Tool Selection
- **H**: Switch to Hand tool (pan/drag)
- **R**: Switch to Rectangle tool
- **P**: Switch to Polygon tool

### Actions
- **Ctrl+Z**: Undo last annotation change (up to 5 changes)
- **Ctrl+Y**: Redo annotation change (up to 5 changes)
- **Ctrl+S**: Save/Export annotations
- **Ctrl+C**: Copy selected annotation
- **Ctrl+V**: Paste copied annotation
- **Delete/Backspace**: Delete selected annotation
- **Enter**: Finish polygon drawing
- **Escape**: Cancel current drawing operation

## Annotation Data Format

The exported JSON file contains:
```json
{
  "pdfDimensions": {
    "width": 612,
    "height": 792
  },
  "annotations": [
    {
      "id": "unique-uuid",
      "type": "rectangle",
      "pageIndex": 0,
      "coordinates": {
        "x": 100,
        "y": 200,
        "width": 150,
        "height": 100
      },
      "color": "#ff0000"
    },
    {
      "id": "unique-uuid",
      "type": "polygon",
      "pageIndex": 0,
      "coordinates": {
        "points": [
          {"x": 300, "y": 400},
          {"x": 350, "y": 450},
          {"x": 320, "y": 500}
        ]
      },
      "color": "#ff0000"
    }
  ]
}
```

## Export Formats

### JSON Export (💾 button)
- Contains annotation coordinates and metadata
- Preserves original PDF dimensions for accurate positioning
- Includes unique IDs for each annotation
- Suitable for data processing and re-importing

### Annotated PDF Export (📄✏️ button)
- Creates a new PDF with annotations drawn directly on the original document
- Rectangles are drawn as outlined boxes
- Polygons are drawn as connected line segments
- Maintains original PDF quality and formatting
- Ready for sharing, printing, or archiving

## Technical Details

- **PDF Processing**: Uses PDF.js library to render PDF pages at 150 DPI for display
- **Coordinate System**:
  - Canvas coordinates used for display and interaction
  - Automatic conversion to PDF coordinates for export
  - Maintains accuracy across different zoom levels and display sizes
- **Canvas Rendering**: HTML5 Canvas for interactive drawing and display
- **State Management**: React hooks for managing application state
- **Unique IDs**: UUID v4 for generating unique annotation identifiers

## Dependencies

- React 19.1.0
- PDF.js (pdfjs-dist) - for PDF rendering and processing
- PDF-lib - for creating annotated PDF exports
- UUID library for unique ID generation
- Vite for development and building

## Responsive Design

The application is fully responsive and adapts to different screen sizes:

### Screen Size Support
- **Mobile (up to 767px)**: Compact toolbar, stacked layout, touch-friendly controls
- **Tablet (768px - 1199px)**: Medium-sized controls, optimized for touch interaction
- **Desktop (1200px - 1919px)**: Full-featured interface with optimal spacing
- **Large Screens (1920px - 2559px)**: Enhanced sizing for better visibility
- **Ultra-wide (2560px+)**: Maximum sizing for professional workstations

### Adaptive Features
- **Toolbar Scaling**: Icons and buttons scale with screen size using CSS clamp()
- **Canvas Optimization**: PDF canvas automatically constrains to viewport
- **Auto-fit Zoom**: 📐 button calculates optimal zoom for current screen
- **Flexible Sidebar**: Annotations panel adapts width based on available space
- **Touch Support**: Mobile-optimized touch targets and gestures

## Browser Compatibility

Works in all modern browsers that support:
- HTML5 Canvas
- File API
- ES6+ JavaScript features
- CSS clamp() function (for responsive scaling)

## Test File

A test PDF file (`test_document.pdf`) is included for testing the annotation functionality. You can also create your own test PDF by running:

```bash
python3 create_test_pdf.py
```
