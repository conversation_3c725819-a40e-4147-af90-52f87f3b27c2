import React, { useState } from 'react'
import { useAnnotationsHandler } from '../components/AnnotationsHandler'

// Debug test component to isolate undo functionality
const UndoDebugTest = () => {
  const [currentPdfIndex] = useState(0)
  const [currentPageIndex] = useState(0)
  
  const {
    canUndo,
    canRedo,
    getCurrentAnnotations,
    updateCurrentAnnotations,
    undoAnnotations,
    redoAnnotations,
    deleteAnnotation
  } = useAnnotationsHandler(currentPdfIndex, currentPageIndex)

  const addBox = () => {
    const currentAnnotations = getCurrentAnnotations()
    const newBox = {
      id: `box-${Date.now()}`,
      type: 'rectangle',
      pageIndex: currentPageIndex,
      x: Math.random() * 100,
      y: Math.random() * 100,
      width: 50,
      height: 50,
      color: '#ff0000',
      label: `Box ${currentAnnotations.length + 1}`
    }
    console.log('➕ Adding box:', newBox.label)
    updateCurrentAnnotations([...currentAnnotations, newBox])
  }

  const deleteLastBox = () => {
    const currentAnnotations = getCurrentAnnotations()
    if (currentAnnotations.length > 0) {
      const lastBox = currentAnnotations[currentAnnotations.length - 1]
      console.log('🗑️ Deleting last box:', lastBox.label)
      deleteAnnotation(lastBox.id)
    }
  }

  const currentAnnotations = getCurrentAnnotations()

  return (
    <div style={{ 
      padding: '20px', 
      border: '2px solid #333', 
      margin: '20px',
      backgroundColor: '#f5f5f5'
    }}>
      <h3>🐛 Undo Debug Test</h3>
      
      <div style={{ marginBottom: '15px' }}>
        <button onClick={addBox} style={{ marginRight: '10px' }}>
          ➕ Add Box
        </button>
        <button 
          onClick={deleteLastBox} 
          disabled={currentAnnotations.length === 0}
          style={{ marginRight: '10px' }}
        >
          🗑️ Delete Last Box
        </button>
        <button 
          onClick={undoAnnotations} 
          disabled={!canUndo}
          style={{ marginRight: '10px' }}
        >
          ↶ Undo {canUndo ? '✓' : '✗'}
        </button>
        <button 
          onClick={redoAnnotations} 
          disabled={!canRedo}
        >
          ↷ Redo {canRedo ? '✓' : '✗'}
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <strong>Current Boxes: {currentAnnotations.length}</strong>
        <div style={{ marginTop: '5px' }}>
          {currentAnnotations.map((box, index) => (
            <div key={box.id} style={{ 
              padding: '5px', 
              margin: '2px 0', 
              backgroundColor: '#fff',
              border: '1px solid #ccc',
              borderRadius: '3px'
            }}>
              {index + 1}. {box.label} (ID: {box.id.slice(-8)})
            </div>
          ))}
          {currentAnnotations.length === 0 && (
            <div style={{ fontStyle: 'italic', color: '#666' }}>No boxes</div>
          )}
        </div>
      </div>

      <div style={{ fontSize: '12px', color: '#666' }}>
        <div>Can Undo: {canUndo ? 'YES' : 'NO'}</div>
        <div>Can Redo: {canRedo ? 'YES' : 'NO'}</div>
      </div>

      <div style={{ marginTop: '15px', fontSize: '14px' }}>
        <strong>Test Scenario:</strong>
        <ol>
          <li>Click "Add Box" twice (should have 2 boxes)</li>
          <li>Click "Delete Last Box" (should have 1 box)</li>
          <li>Click "Undo" (should restore to 2 boxes)</li>
          <li>Check console for debug logs</li>
        </ol>
      </div>
    </div>
  )
}

export default UndoDebugTest
